import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Providers for state management
final themeModeProvider = StateProvider<ThemeMode>((ref) => ThemeMode.light);
final userListProvider = StateProvider<List<Map<String, String>>>(
  (ref) => [
    {'name': '<PERSON>', 'email': '<EMAIL>', 'role': 'Admin'},
    {'name': '<PERSON>', 'email': '<EMAIL>', 'role': 'User'},
  ],
);
final authProvider = StateProvider<Map<String, String>?>((ref) => null);
