import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/auth_models.dart';
import '../services/auth_service.dart';

// Providers for state management
final themeModeProvider = StateProvider<ThemeMode>((ref) => ThemeMode.light);
final userListProvider = StateProvider<List<Map<String, String>>>(
  (ref) => [
    {'name': '<PERSON>', 'email': '<EMAIL>', 'role': 'Admin'},
    {'name': '<PERSON>', 'email': '<EMAIL>', 'role': 'User'},
  ],
);

// Auth service provider
final authServiceProvider = Provider<AuthService>((ref) => AuthService());

// Auth state provider
final authProvider = StateNotifierProvider<AuthNotifier, AuthState>((ref) {
  return AuthNotifier(ref.read(authServiceProvider));
});

// Auth state classes
@immutable
class AuthState {
  final AuthUser? user;
  final bool isLoading;
  final String? error;
  final bool isAuthenticated;

  const AuthState({
    this.user,
    this.isLoading = false,
    this.error,
    this.isAuthenticated = false,
  });

  AuthState copyWith({
    AuthUser? user,
    bool? isLoading,
    String? error,
    bool? isAuthenticated,
  }) {
    return AuthState(
      user: user ?? this.user,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      isAuthenticated: isAuthenticated ?? this.isAuthenticated,
    );
  }
}

// Auth notifier
class AuthNotifier extends StateNotifier<AuthState> {
  final AuthService _authService;

  AuthNotifier(this._authService) : super(const AuthState()) {
    _checkAuthStatus();
  }

  Future<void> _checkAuthStatus() async {
    state = state.copyWith(isLoading: true);

    try {
      await _authService.initialize();
      final user = await _authService.getCurrentUser();
      final isAuthenticated = await _authService.isAuthenticated();

      state = state.copyWith(
        user: user,
        isAuthenticated: isAuthenticated,
        isLoading: false,
        error: null,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
        isAuthenticated: false,
      );
    }
  }

  Future<bool> login(String username, String password) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final request = LoginRequest(username: username, password: password);
      final response = await _authService.login(request);

      final user = await _authService.getCurrentUser();

      state = state.copyWith(
        user: user,
        isAuthenticated: true,
        isLoading: false,
        error: null,
      );

      return true;
    } on AuthError catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.message,
        isAuthenticated: false,
      );
      return false;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'An unexpected error occurred',
        isAuthenticated: false,
      );
      return false;
    }
  }

  Future<void> logout() async {
    state = state.copyWith(isLoading: true);

    try {
      await _authService.logout();
      state = const AuthState(isAuthenticated: false);
    } catch (e) {
      state = state.copyWith(isLoading: false, error: 'Logout failed');
    }
  }

  void clearError() {
    state = state.copyWith(error: null);
  }
}
