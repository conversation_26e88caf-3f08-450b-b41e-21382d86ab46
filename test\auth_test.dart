import 'package:flutter_test/flutter_test.dart';
import 'package:myapp/models/auth_models.dart';
import 'package:myapp/services/auth_service.dart';

void main() {
  group('Authentication Tests', () {
    test('LoginRequest model should serialize correctly', () {
      final request = LoginRequest(
        username: 'testuser',
        password: 'testpass123',
      );

      final json = request.toJson();
      expect(json['username'], equals('testuser'));
      expect(json['password'], equals('testpass123'));

      final fromJson = LoginRequest.fromJson(json);
      expect(fromJson.username, equals('testuser'));
      expect(fromJson.password, equals('testpass123'));
    });

    test('LoginResponse model should serialize correctly', () {
      final response = LoginResponse(
        accessToken: 'test-token',
        tenantId: 'tenant-123',
        officeCode: 'office-456',
        username: 'testuser',
        refreshToken: 'refresh-token',
        tokenType: 'Bearer',
        expiresIn: 3600,
      );

      final json = response.toJson();
      expect(json['accessToken'], equals('test-token'));
      expect(json['tenantId'], equals('tenant-123'));
      expect(json['officeCode'], equals('office-456'));
      expect(json['username'], equals('testuser'));

      final fromJson = LoginResponse.fromJson(json);
      expect(fromJson.accessToken, equals('test-token'));
      expect(fromJson.tenantId, equals('tenant-123'));
      expect(fromJson.officeCode, equals('office-456'));
      expect(fromJson.username, equals('testuser'));
    });

    test('AuthUser model should serialize correctly', () {
      final user = AuthUser(
        username: 'testuser',
        tenantId: 'tenant-123',
        officeCode: 'office-456',
        accessToken: 'test-token',
        tokenExpiry: DateTime(2024, 12, 31, 23, 59, 59),
      );

      final json = user.toJson();
      expect(json['username'], equals('testuser'));
      expect(json['tenantId'], equals('tenant-123'));
      expect(json['officeCode'], equals('office-456'));
      expect(json['accessToken'], equals('test-token'));

      final fromJson = AuthUser.fromJson(json);
      expect(fromJson.username, equals('testuser'));
      expect(fromJson.tenantId, equals('tenant-123'));
      expect(fromJson.officeCode, equals('office-456'));
      expect(fromJson.accessToken, equals('test-token'));
    });

    test('AuthError model should serialize correctly', () {
      final error = AuthError(
        message: 'Invalid credentials',
        code: 'AUTH_ERROR',
        statusCode: 401,
      );

      final json = error.toJson();
      expect(json['message'], equals('Invalid credentials'));
      expect(json['code'], equals('AUTH_ERROR'));
      expect(json['statusCode'], equals(401));

      final fromJson = AuthError.fromJson(json);
      expect(fromJson.message, equals('Invalid credentials'));
      expect(fromJson.code, equals('AUTH_ERROR'));
      expect(fromJson.statusCode, equals(401));
    });

    test('AuthService should initialize correctly', () {
      final authService = AuthService();
      expect(authService, isNotNull);
      expect(authService.dio, isNotNull);
    });
  });
}
