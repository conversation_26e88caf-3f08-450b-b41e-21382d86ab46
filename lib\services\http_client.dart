import 'package:dio/dio.dart';
import 'auth_service.dart';

class HttpClient {
  static final HttpClient _instance = HttpClient._internal();
  factory HttpClient() => _instance;
  
  late final Dio _dio;
  late final AuthService _authService;

  HttpClient._internal() {
    _dio = Dio();
    _authService = AuthService();
    _setupInterceptors();
  }

  void _setupInterceptors() {
    _dio.options.connectTimeout = const Duration(seconds: 30);
    _dio.options.receiveTimeout = const Duration(seconds: 30);
    
    _dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) async {
          // Skip auth header for login endpoint
          if (options.path.contains('/auth/authenticate')) {
            options.headers['Content-Type'] = 'application/json';
            options.headers['accept'] = '*/*';
            handler.next(options);
            return;
          }

          // Add auth header for other requests
          final token = await _authService.getAccessToken();
          if (token != null) {
            // Check if token is expired
            if (await _authService.isTokenExpired()) {
              await _authService.logout();
              handler.reject(
                DioException(
                  requestOptions: options,
                  type: DioExceptionType.cancel,
                  message: 'Token expired - user logged out',
                ),
              );
              return;
            }
            options.headers['Authorization'] = 'Bearer $token';
          }
          
          options.headers['Content-Type'] = 'application/json';
          options.headers['accept'] = '*/*';
          handler.next(options);
        },
        onError: (error, handler) async {
          // Handle 401 unauthorized responses
          if (error.response?.statusCode == 401) {
            await _authService.logout();
            // You might want to navigate to login screen here
            // This would typically be handled by a global error handler
          }
          handler.next(error);
        },
      ),
    );
  }

  Dio get dio => _dio;

  // Convenience methods for common HTTP operations
  Future<Response> get(
    String path, {
    Map<String, dynamic>? queryParameters,
    Options? options,
  }) async {
    return await _dio.get(
      path,
      queryParameters: queryParameters,
      options: options,
    );
  }

  Future<Response> post(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
  }) async {
    return await _dio.post(
      path,
      data: data,
      queryParameters: queryParameters,
      options: options,
    );
  }

  Future<Response> put(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
  }) async {
    return await _dio.put(
      path,
      data: data,
      queryParameters: queryParameters,
      options: options,
    );
  }

  Future<Response> delete(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
  }) async {
    return await _dio.delete(
      path,
      data: data,
      queryParameters: queryParameters,
      options: options,
    );
  }
}
