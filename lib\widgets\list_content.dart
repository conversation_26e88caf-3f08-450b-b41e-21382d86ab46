import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/app_providers.dart';

class ListContent extends ConsumerWidget {
  ListContent({super.key});

  final searchController = TextEditingController();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final users = ref.watch(userListProvider);
    final filteredUsers = StateProvider<List<Map<String, String>>>(
      (ref) => users,
    );

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'User List',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          // Search Bar
          TextField(
            controller: searchController,
            decoration: InputDecoration(
              hintText: 'Search users...',
              prefixIcon: const Icon(Icons.search),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            onChanged: (value) {
              ref.read(filteredUsers.notifier).state = users
                  .where(
                    (user) =>
                        user['name']!.toLowerCase().contains(
                          value.toLowerCase(),
                        ) ||
                        user['email']!.toLowerCase().contains(
                          value.toLowerCase(),
                        ),
                  )
                  .toList();
            },
          ),
          const SizedBox(height: 16),
          // User Data Table
          Card(
            child: Consumer(
              builder: (context, ref, child) {
                final filteredUsersList = ref.watch(filteredUsers);
                return SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: DataTable(
                    columns: const [
                      DataColumn(label: Text('Name')),
                      DataColumn(label: Text('Email')),
                      DataColumn(label: Text('Role')),
                    ],
                    rows: filteredUsersList
                        .map(
                          (user) => DataRow(
                            cells: [
                              DataCell(Text(user['name']!)),
                              DataCell(Text(user['email']!)),
                              DataCell(Text(user['role']!)),
                            ],
                          ),
                        )
                        .toList(),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
