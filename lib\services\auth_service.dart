import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:dart_jsonwebtoken/dart_jsonwebtoken.dart';
import 'package:intl/intl.dart';
import '../models/auth_models.dart';

class AuthService {
  static const String _baseUrl = 'http://46.37.122.247:9502/auth-service';
  static const String _loginEndpoint = '/auth/authenticate';
  
  // Storage keys
  static const String _accessTokenKey = 'access_token';
  static const String _tenantIdKey = 'tenant_id';
  static const String _officeCodeKey = 'office_code';
  static const String _usernameKey = 'username';
  static const String _tokenExpiryKey = 'token_expiry';

  final Dio _dio;
  final FlutterSecureStorage _secureStorage;
  late final SharedPreferences _prefs;

  AuthService._internal()
      : _dio = Dio(),
        _secureStorage = const FlutterSecureStorage(
          aOptions: AndroidOptions(
            encryptedSharedPreferences: true,
          ),
          iOptions: IOSOptions(
            accessibility: KeychainAccessibility.first_unlock_this_device,
          ),
        );

  static final AuthService _instance = AuthService._internal();
  factory AuthService() => _instance;

  Future<void> initialize() async {
    _prefs = await SharedPreferences.getInstance();
    _setupDioInterceptors();
  }

  void _setupDioInterceptors() {
    _dio.options.baseUrl = _baseUrl;
    _dio.options.connectTimeout = const Duration(seconds: 30);
    _dio.options.receiveTimeout = const Duration(seconds: 30);
    
    _dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) async {
          // Skip auth header for login endpoint
          if (options.path.contains(_loginEndpoint)) {
            options.headers['Content-Type'] = 'application/json';
            options.headers['accept'] = '*/*';
            handler.next(options);
            return;
          }

          // Add auth header for other requests
          final token = await getAccessToken();
          if (token != null) {
            // Check if token is expired
            if (await isTokenExpired()) {
              await logout();
              handler.reject(
                DioException(
                  requestOptions: options,
                  type: DioExceptionType.cancel,
                  message: 'Token expired',
                ),
              );
              return;
            }
            options.headers['Authorization'] = 'Bearer $token';
          }
          
          options.headers['Content-Type'] = 'application/json';
          options.headers['accept'] = '*/*';
          handler.next(options);
        },
        onError: (error, handler) {
          if (error.response?.statusCode == 401) {
            logout();
          }
          handler.next(error);
        },
      ),
    );
  }

  Future<LoginResponse> login(LoginRequest request) async {
    try {
      final response = await _dio.post(
        _loginEndpoint,
        data: request.toJson(),
      );

      if (response.statusCode == 200) {
        final loginResponse = LoginResponse.fromJson(response.data);
        await _storeAuthData(loginResponse);
        return loginResponse;
      } else {
        throw AuthError(
          message: 'Login failed',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      String message = 'Network error occurred';
      
      if (e.response != null) {
        message = e.response?.data['message'] ?? 
                 'Login failed with status ${e.response?.statusCode}';
      } else if (e.type == DioExceptionType.connectionTimeout) {
        message = 'Connection timeout';
      } else if (e.type == DioExceptionType.receiveTimeout) {
        message = 'Request timeout';
      }

      throw AuthError(
        message: message,
        statusCode: e.response?.statusCode,
      );
    } catch (e) {
      throw AuthError(message: 'An unexpected error occurred: $e');
    }
  }

  Future<void> _storeAuthData(LoginResponse response) async {
    // Store access token securely
    await _secureStorage.write(key: _accessTokenKey, value: response.accessToken);
    
    // Store other data in shared preferences
    await _prefs.setString(_tenantIdKey, response.tenantId);
    await _prefs.setString(_officeCodeKey, response.officeCode);
    await _prefs.setString(_usernameKey, response.username);
    
    // Decode JWT to get expiry time
    final tokenExpiry = _getTokenExpiry(response.accessToken);
    if (tokenExpiry != null) {
      final expiryString = DateFormat('yyyy-MM-dd HH:mm:ss').format(tokenExpiry);
      await _prefs.setString(_tokenExpiryKey, expiryString);
    }
  }

  DateTime? _getTokenExpiry(String token) {
    try {
      // Decode JWT without verification (as requested)
      final jwt = JWT.decode(token);
      final payload = jwt.payload;
      
      if (payload is Map<String, dynamic> && payload.containsKey('exp')) {
        final exp = payload['exp'] as int;
        return DateTime.fromMillisecondsSinceEpoch(exp * 1000);
      }
    } catch (e) {
      // If decoding fails, return null
      return null;
    }
    return null;
  }

  Future<String?> getAccessToken() async {
    return await _secureStorage.read(key: _accessTokenKey);
  }

  Future<AuthUser?> getCurrentUser() async {
    final token = await getAccessToken();
    final tenantId = _prefs.getString(_tenantIdKey);
    final officeCode = _prefs.getString(_officeCodeKey);
    final username = _prefs.getString(_usernameKey);
    final expiryString = _prefs.getString(_tokenExpiryKey);

    if (token != null && tenantId != null && officeCode != null && 
        username != null && expiryString != null) {
      try {
        final expiry = DateFormat('yyyy-MM-dd HH:mm:ss').parse(expiryString);
        return AuthUser(
          username: username,
          tenantId: tenantId,
          officeCode: officeCode,
          accessToken: token,
          tokenExpiry: expiry,
        );
      } catch (e) {
        // If parsing fails, return null
        return null;
      }
    }
    return null;
  }

  Future<bool> isTokenExpired() async {
    final expiryString = _prefs.getString(_tokenExpiryKey);
    if (expiryString == null) return true;

    try {
      final expiry = DateFormat('yyyy-MM-dd HH:mm:ss').parse(expiryString);
      return DateTime.now().isAfter(expiry);
    } catch (e) {
      return true;
    }
  }

  Future<bool> isAuthenticated() async {
    final token = await getAccessToken();
    if (token == null) return false;
    
    return !(await isTokenExpired());
  }

  Future<void> logout() async {
    // Clear secure storage
    await _secureStorage.delete(key: _accessTokenKey);
    
    // Clear shared preferences
    await _prefs.remove(_tenantIdKey);
    await _prefs.remove(_officeCodeKey);
    await _prefs.remove(_usernameKey);
    await _prefs.remove(_tokenExpiryKey);
  }

  Dio get dio => _dio;
}
